import React from 'react';
import { DashboardProvider } from '../../contexts/DashboardContext';
import { useAuth } from '../../contexts/AuthContext';
import Layout from './Layout';

const DashboardLayout: React.FC = () => {
  const { logoutCounter } = useAuth();

  return (
    <DashboardProvider key={`dashboard-${logoutCounter}`}>
      <Layout />
    </DashboardProvider>
  );
};

export default DashboardLayout;

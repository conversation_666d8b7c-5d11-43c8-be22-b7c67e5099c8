@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force light theme and prevent dark mode */
:root {
  color-scheme: light only;
}

html {
  color-scheme: light only;
}

/* Override any dark mode preferences */
@media (prefers-color-scheme: light) {
  :root {
    color-scheme: light only;
  }

  html {
    color-scheme: light only;
  }

  body {
    background-color: white;
    color: black;
  }
}

/* Ensure all elements use light theme */
* {
  color-scheme: light only;
}

/* Currency formatting styles for Excel-like table display */
.currency-cell {
  font-family: 'Courier New', monospace;
  text-align: right;
}

.currency-symbol {
  float: left;
}

.currency-amount {
  text-align: right;
}

/* Ensure proper spacing in currency cells */
.currency-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Table cell alignment utilities */
.table-cell-currency {
  text-align: right;
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
  -webkit-font-feature-settings: "tnum";
  -moz-font-feature-settings: "tnum";
}

.table-cell-number {
  text-align: right;
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
  -webkit-font-feature-settings: "tnum";
  -moz-font-feature-settings: "tnum";
}

.table-cell-text {
  text-align: left;
}

/* Monospace font for better number alignment */
.monospace-numbers {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
  -webkit-font-feature-settings: "tnum";
  -moz-font-feature-settings: "tnum";
}

/* Custom scrollbar styles */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-track-gray-800 {
    scrollbar-color: #374151 #1f2937;
  }
  
  .scrollbar-thumb-gray-600 {
    scrollbar-color: #4b5563 #1f2937;
  }
  
  /* Webkit scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-track-gray-800::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 3px;
  }
  
  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 3px;
  }
  
  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }
}



/* Custom focus styles for better accessibility */
*:focus {
  outline-color: #3b82f6;
}

/* Safari-specific input styling fixes */
input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Ensure consistent button styling across browsers */
button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Fix for Safari radio button styling */
input[type="radio"] {
  -webkit-appearance: radio;
  -moz-appearance: radio;
  appearance: radio;
}

input[type="checkbox"] {
  -webkit-appearance: checkbox;
  -moz-appearance: checkbox;
  appearance: checkbox;
}

/* Ensure consistent table cell alignment in Safari */
.table-cell-currency,
.table-cell-number {
  text-align: right;
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum";
  -webkit-font-feature-settings: "tnum";
  -moz-font-feature-settings: "tnum";
  -ms-font-feature-settings: "tnum";
}

/* Safari-specific date input fixes */
input[type="date"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Ensure proper text rendering in Safari */
body, input, textarea, select, button {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Fix for Safari flexbox issues */
.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

/* Ensure proper grid support in Safari */
.grid {
  display: -ms-grid;
  display: grid;
}

/* Safari-specific border-radius fixes */
.rounded, .rounded-lg, .rounded-md {
  -webkit-border-radius: inherit;
  border-radius: inherit;
}
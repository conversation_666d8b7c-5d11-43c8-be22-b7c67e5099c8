import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { DashboardProvider } from './contexts/DashboardContext';
import LoginPage from './components/auth/LoginPage';
import Layout from './components/layout/Layout';

const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading, isLoggingOut, logoutCounter } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Handle navigation after authentication changes
  useEffect(() => {
    if (!isLoading && !isLoggingOut) {
      if (isAuthenticated && location.pathname === '/login') {
        // User just logged in, redirect to dashboard
        navigate('/dashboard', { replace: true });
      } else if (!isAuthenticated && location.pathname.startsWith('/dashboard')) {
        // User is not authenticated but trying to access dashboard, redirect to login
        navigate('/login', { replace: true });
      }
    }
  }, [isAuthenticated, isLoading, isLoggingOut, location.pathname, navigate]);

  // Show loading spinner while checking session or logging out
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h4 className="text-xl font-semibold text-gray-700 mb-2">Loading...</h4>
          <p className="text-gray-600">Please wait while we initialize the application</p>
        </div>
      </div>
    );
  }

  // Show logging out spinner for smooth transition
  if (isLoggingOut) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h4 className="text-xl font-semibold text-gray-700 mb-2">Signing Out...</h4>
          <p className="text-gray-600">Please wait while we sign you out</p>
        </div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Login route - always accessible */}
      <Route path="/login" element={<LoginPage key={`login-${logoutCounter}`} />} />

      {/* Protected dashboard routes */}
      <Route path="/dashboard/*" element={
        isAuthenticated ? (
          <DashboardProvider key={`dashboard-${logoutCounter}`}>
            <Layout />
          </DashboardProvider>
        ) : (
          <Navigate to="/login" replace />
        )
      } />

      {/* Default route - always redirect to login */}
      <Route path="/" element={<Navigate to="/login" replace />} />

      {/* Catch all other routes and redirect to login */}
      <Route path="*" element={<Navigate to="/login" replace />} />
    </Routes>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppContent />
      </Router>
    </AuthProvider>
  );
}

export default App;
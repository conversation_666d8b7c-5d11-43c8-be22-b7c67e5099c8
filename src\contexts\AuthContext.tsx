import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { User, AuthState } from '../types';

interface AuthContextType extends AuthState {
  login: (username: string, password: string, rememberMe: boolean) => Promise<boolean>;
  logout: () => void;
  resetPassword: (email: string) => Promise<boolean>;
  isLoading: boolean;
  isLoggingOut: boolean;
  logoutCounter: number;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  rememberMe: false,
};

type AuthAction =
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; rememberMe: boolean } }
  | { type: 'LOGOUT' }
  | { type: 'RESTORE_SESSION'; payload: { user: User; rememberMe: boolean } };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        rememberMe: action.payload.rememberMe,
      };
    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        rememberMe: false,
      };
    case 'RESTORE_SESSION':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload.user,
        rememberMe: action.payload.rememberMe,
      };
    default:
      return state;
  }
};

// Simple session storage keys
const SESSION_KEYS = {
  USER: 'insuranceApp_user',
  REMEMBER_ME: 'insuranceApp_rememberMe',
};

// Helper functions for session management
const clearSessionData = () => {
  localStorage.removeItem(SESSION_KEYS.USER);
  localStorage.removeItem(SESSION_KEYS.REMEMBER_ME);
};

const clearAllApplicationData = () => {
  // Clear all application data for fresh start (but preserve theme)
  const theme = localStorage.getItem('theme');
  localStorage.clear();
  if (theme) {
    localStorage.setItem('theme', theme);
  }
  console.log('🧹 All application data cleared for fresh start');
};

const clearApplicationDataExceptSession = () => {
  // Clear application data but preserve session for login persistence
  const theme = localStorage.getItem('theme');
  const savedUser = localStorage.getItem(SESSION_KEYS.USER);
  const savedRememberMe = localStorage.getItem(SESSION_KEYS.REMEMBER_ME);

  localStorage.clear();

  // Restore preserved data
  if (theme) {
    localStorage.setItem('theme', theme);
  }
  if (savedUser) {
    localStorage.setItem(SESSION_KEYS.USER, savedUser);
  }
  if (savedRememberMe) {
    localStorage.setItem(SESSION_KEYS.REMEMBER_ME, savedRememberMe);
  }

  console.log('🧹 Application data cleared but session preserved');
};

const saveSessionData = (user: User, rememberMe: boolean) => {
  localStorage.setItem(SESSION_KEYS.USER, JSON.stringify(user));
  localStorage.setItem(SESSION_KEYS.REMEMBER_ME, rememberMe.toString());

  console.log('🔐 Session saved:', {
    username: user.username,
    rememberMe
  });
};

// Hardcoded credentials for demo
const DEMO_CREDENTIALS = {
  username: 'admin',
  password: 'password123',
  user: {
    id: '1',
    username: 'admin',
    name: 'John Admin',
    email: '<EMAIL>',
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const [isLoading, setIsLoading] = React.useState(true);
  const [isLoggingOut, setIsLoggingOut] = React.useState(false);
  const [logoutCounter, setLogoutCounter] = React.useState(0);

  const checkSession = () => {
    console.log('🔍 Checking for existing session...');

    const savedUser = localStorage.getItem(SESSION_KEYS.USER);
    const savedRememberMe = localStorage.getItem(SESSION_KEYS.REMEMBER_ME);

    // Check if we have session data
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        const rememberMe = savedRememberMe === 'true';

        console.log('✅ Valid session found, restoring user:', {
          username: user.username,
          rememberMe
        });

        dispatch({
          type: 'RESTORE_SESSION',
          payload: {
            user,
            rememberMe,
          }
        });
      } catch (error) {
        console.log('❌ Error parsing saved user data, clearing session');
        clearSessionData();
      }
    } else {
      console.log('❌ No saved session found');
    }

    setIsLoading(false);
  };

  useEffect(() => {
    checkSession();
  }, []);

  const login = async (username: string, password: string, rememberMe: boolean): Promise<boolean> => {
    console.log('🔐 Login attempt:', { username, rememberMe });

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (username === DEMO_CREDENTIALS.username && password === DEMO_CREDENTIALS.password) {
      console.log('✅ Login successful - ensuring fresh start');

      // Clear application data but preserve any existing session for smooth transition
      clearApplicationDataExceptSession();

      // Update state
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: DEMO_CREDENTIALS.user,
          rememberMe,
        }
      });

      // Save session data for persistence across refreshes
      saveSessionData(DEMO_CREDENTIALS.user, rememberMe);

      return true;
    }

    console.log('❌ Login failed: Invalid credentials');
    return false;
  };

  const logout = () => {
    console.log('🚪 Logging out user');

    // Set logging out state for smooth transition
    setIsLoggingOut(true);

    // Small delay for smooth UX
    setTimeout(() => {
      // Clear all application data for fresh start
      clearAllApplicationData();

      // Update authentication state
      dispatch({ type: 'LOGOUT' });

      // Increment logout counter to force fresh components
      setLogoutCounter(prev => prev + 1);

      // Reset logging out state
      setIsLoggingOut(false);

      console.log('✅ Logout complete, redirecting to fresh login');
    }, 300); // 300ms delay for smooth transition
  };

  const resetPassword = async (_email: string): Promise<boolean> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    return true;
  };

  return (
    <AuthContext.Provider value={{
      ...state,
      login,
      logout,
      resetPassword,
      isLoading,
      isLoggingOut,
      logoutCounter,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};